<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>2025年全国大学生电子设计竞赛 - 简易自行瞄准装置(E题)</title>
    <style>
        body {
            font-family: "宋体", SimSun, serif;
            font-size: 12pt; /* 小四号对应12pt */
            line-height: 22pt; /* 固定值22磅 */
            margin: 0 auto;
            padding: 20px;
            max-width: 900px;
        }

        h1 {
            font-family: "黑体", SimHei, sans-serif;
            font-size: 16pt; /* 三号对应16pt */
            text-align: center;
            margin: 20px 0;
        }

        h2 {
            font-family: "黑体", SimHei, sans-serif;
            font-size: 14pt; /* 四号对应14pt */
            margin: 18px 0 10px 0;
        }

        p {
            text-indent: 2em; /* 首行缩进两字符 */
            margin: 10px 0;
        }

        .abstract {
            margin: 20px 0;
            padding: 0 20px;
        }

        .keywords {
            margin: 10px 0 20px 0;
            text-indent: 2em;
        }

        .equation {
            text-align: center;
            margin: 15px 0;
        }

        .image-caption {
            text-align: center;
            margin: 10px 0 20px 0;
            font-style: italic;
        }

        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }

        table, th, td {
            border: 1px solid #000;
            padding: 8px 12px;
            text-align: center;
        }

        th {
            background-color: #f0f0f0;
        }

        .appendix-title {
            margin-top: 30px;
        }

        ul {
            margin: 10px 0 10px 2em;
            padding-left: 1em;
        }

        li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <h1>2025年全国大学生电子设计竞赛</h1>
    <h1>简易自行瞄准装置(E题)</h1>
    <h1>简易自行瞄准装置(E题)</h1>

    <div class="abstract">
        <h2>摘 要</h2>
        <p>本设计采用双MCU架构，以TI MSPM0G3507和STM32F407为核心控制器，结合视觉识别和激光瞄准技术，实现简易自行瞄准装置。该装置包括自动寻迹小车和智能瞄准模块两部分，能够实现沿轨迹自动寻迹行驶和基于视觉反馈的激光精确瞄准功能。小车模块主要包括MSPM0主控、TB6612FNG电机驱动、MG513直流减速电机、八路灰度传感器等；瞄准模块主要包括STM32F407主控、MaixCam Pro视觉模块、VMM42_V5.0步进电机云台、蓝紫激光笔等。在寻迹控制中，采用八路灰度传感器检测黑线轨迹，通过PID算法实现精确跟踪；在瞄准控制中，使用MaixCam Pro进行目标识别定位，STM32F407控制步进电机云台实现激光精确瞄准。两个模块采用独立电源开关控制，确保系统的可靠性和安全性。经过测试验证，本设计具有寻迹精度高、视觉识别准确、瞄准精度高等优点。</p>
    </div>

    <p class="keywords"><strong>关键词：</strong>MSPM0；STM32F407；视觉识别；激光瞄准；自动寻迹；PID控制</p>

    <h2>一、方案论证</h2>

    <h2>1.1 系统基本方案</h2>

    <h2>1.1.1 控制方案设计</h2>
    <p>根据题目要求，采用双MCU分布式控制架构，将系统分为小车模块和瞄准模块两大部分。小车模块包括MSPM0主控、电机驱动、灰度寻迹、电源管理等子模块；瞄准模块包括STM32F407主控、视觉识别、云台控制、激光控制等子模块。小车模块使用MSPM0G3507控制MG513电机实现精确寻迹；瞄准模块使用STM32F407配合MaixCam Pro实现视觉识别和云台控制。两模块通过通信接口协调工作，系统的总体设计框图如图1所示。</p>

    <div class="image-caption">
        图1 系统整体方案框图<br>
        <img src="images/system_block_diagram.jpg" alt="系统整体方案框图" style="max-width:100%;height:auto;">
    </div>

    <h2>1.1.2 机械结构方案与设计</h2>
    <p>根据题目要求，小车尺寸不大于25cm（长）×15cm（宽）×25cm（高），采用三轮驱动结构，前轮为两个MG513直流减速电机驱动，后轮为单个万向轮支撑。瞄准模块安装在小车顶部，由VMM42_V5.0步进电机构成的二维云台支撑，确保激光笔能够在水平和垂直方向精确转动。八路灰度传感器阵列安装在小车前端底部，距离地面约3-5mm，确保能够准确检测黑线轨迹。MaixCam Pro摄像头安装在云台上，与激光笔同轴安装，实现视觉识别和目标定位。</p>

    <h2>1.2 主要模块方案选择</h2>
    <p>(1) 主控芯片选择</p>
    <p>方案一：采用TI MSPM0G3507，80MHz ARM Cortex-M0+内核，集成丰富的模拟外设，包括12位ADC、比较器、运放等，具有多路PWM输出，适合电机控制和传感器接口。</p>
    <p>方案二：采用STM32F103系列，32位ARM Cortex-M3内核，性能强大但功耗较高，且题目明确要求使用MSPM0系列MCU。</p>
    <p>根据题目要求和性能需求，选择方案一。</p>

    <p>(2) 电机选择</p>
    <p>方案一：采用N20直流减速电机，体积小，功耗低，但扭矩较小，承载能力有限。</p>
    <p>方案二：采用MG513直流减速电机，扭矩大，承载能力强，专为智能小车设计，带编码器反馈。</p>
    <p>考虑到小车需要搭载瞄准模块，重量较大，且采用三轮结构（前轮双电机驱动），选择方案二。三轮结构具有转向灵活、控制简单的优势，前轮双电机差速转向，后轮万向轮提供支撑。</p>

    <p>(3) 电机驱动模块选择</p>
    <p>方案一：采用L298N双H桥电机驱动模块，驱动电流大，但效率较低，发热严重。</p>
    <p>方案二：采用TB6612FNG电机驱动芯片，效率高，体积小，驱动电流适中，适合中型直流电机驱动。</p>
    <p>考虑到系统的功耗和效率要求，选择方案二。</p>

    <p>(4) 寻迹传感器选择</p>
    <p>方案一：采用红外传感器阵列，基于TCRT5000，成本低但易受环境光干扰。</p>
    <p>方案二：采用八路灰度传感器，基于TCRT5000优化设计，抗干扰能力强，检测精度高。</p>
    <p>为了提高寻迹精度和抗干扰能力，选择方案二。</p>

    <p>(5) 瞄准模块主控选择</p>
    <p>方案一：使用MSPM0控制云台和激光，但处理能力有限，难以处理复杂的视觉算法。</p>
    <p>方案二：使用STM32F407作为瞄准模块主控，ARM Cortex-M4内核，处理能力强，适合视觉处理。</p>
    <p>考虑到视觉识别的复杂性，选择方案二。</p>

    <p>(6) 视觉识别模块选择</p>
    <p>方案一：使用OpenMV摄像头，成本低但处理能力有限。</p>
    <p>方案二：使用MaixCam Pro，基于BL808芯片，集成NPU加速器，适合实时图像处理和AI识别。</p>
    <p>为了实现精确的目标识别和定位，选择方案二。</p>

    <p>(7) 激光控制方案选择</p>
    <p>方案一：采用405nm蓝紫激光笔，功率≤10mW，配合PWM调光控制。</p>
    <p>方案二：采用红色激光笔，但题目明确要求使用蓝紫激光笔。</p>
    <p>根据题目要求，选择方案一。</p>

    <h2>二、理论分析与计算</h2>

    <h2>2.1 三轮小车运动学分析</h2>
    <p>本设计采用三轮结构，前轮为两个MG513直流减速电机驱动，后轮为万向轮支撑。三轮小车的运动学模型如下：</p>
    <p>设左前轮速度为$v_L$，右前轮速度为$v_R$，轮距为$L$，则小车的线速度和角速度为：</p>
    <div class="equation">
        $v = \frac{v_L + v_R}{2}$
    </div>
    <div class="equation">
        $\omega = \frac{v_R - v_L}{L}$
    </div>
    <p>小车转弯半径为：</p>
    <div class="equation">
        $R = \frac{L}{2} \cdot \frac{v_L + v_R}{v_R - v_L}$
    </div>
    <p>三轮结构的优势：</p>
    <ul>
        <li>转向灵活，可实现原地转向（$v_L = -v_R$）</li>
        <li>控制简单，只需控制两个驱动电机</li>
        <li>机械结构紧凑，适合小型智能车</li>
    </ul>

    <h2>2.2 自动寻迹控制算法</h2>
    <p>采用PID控制算法实现精确的轨迹跟踪。八路灰度传感器检测黑线位置，计算偏差值，通过PID算法输出控制量调节左右前轮电机转速，实现差速转向。</p>
    <p>设八路灰度传感器编号为0到7，当传感器检测到黑线时输出为1，否则为0。位置偏差计算公式为：</p>
    <div class="equation">
        $error = \frac{\sum_{i=0}^{7} i \cdot sensor_i}{\sum_{i=0}^{7} sensor_i} - 3.5$
    </div>
    <p>PID控制算法的输出为：</p>
    <div class="equation">
        $output = K_p \cdot error + K_i \cdot \sum error + K_d \cdot (error - last\_error)$
    </div>
    <p>其中，$K_p$为比例系数，$K_i$为积分系数，$K_d$为微分系数。</p>
    <p>基于PID输出的三轮小车差速控制：</p>
    <div class="equation">
        $v_{left} = v_{base} - output$
    </div>
    <div class="equation">
        $v_{right} = v_{base} + output$
    </div>
    <p>其中，$v_{base}$为基础速度，$v_{left}$和$v_{right}$分别为左右前轮的目标速度。</p>

    <h2>2.3 视觉识别与瞄准算法</h2>
    <p>采用MaixCam Pro进行目标识别和定位，通过图像处理算法检测目标靶的位置。设图像分辨率为$W \times H$，目标在图像中的像素坐标为$(u, v)$，则目标相对于图像中心的偏移量为：</p>
    <div class="equation">
        $\Delta u = u - \frac{W}{2}, \quad \Delta v = v - \frac{H}{2}$
    </div>
    <p>根据摄像头的视场角和目标距离，计算云台需要调整的角度：</p>
    <div class="equation">
        $\theta_{yaw} = \arctan\left(\frac{\Delta u \cdot \tan(\alpha/2)}{W/2}\right)$
    </div>
    <div class="equation">
        $\theta_{pitch} = \arctan\left(\frac{\Delta v \cdot \tan(\beta/2)}{H/2}\right)$
    </div>
    <p>其中，$\alpha$为水平视场角，$\beta$为垂直视场角。</p>

    <h2>2.4 步进电机控制算法</h2>
    <p>VMM42_V5.0步进电机采用脉冲控制方式，步距角为1.8°，即每转需要200步。云台角度控制公式为：</p>
    <div class="equation">
        $Steps = \frac{\theta_{target}}{1.8°} \times MicroStep$
    </div>
    <p>其中，$\theta_{target}$为目标角度，$MicroStep$为微步进细分数。采用微步进技术可以提高定位精度：</p>
    <div class="equation">
        $Resolution = \frac{1.8°}{MicroStep}$
    </div>
    <p>步进电机的速度控制通过调节脉冲频率实现：</p>
    <div class="equation">
        $f_{pulse} = \frac{RPM \times 200 \times MicroStep}{60}$
    </div>

    <h2>2.5 电机速度控制</h2>
    <p>采用PWM控制电机转速，PWM占空比与电机转速成正比关系。设PWM频率为$f_{PWM}$，占空比为$D$，则电机的平均电压为：</p>
    <div class="equation">
        $V_{avg} = D \cdot V_{supply}$
    </div>
    <p>电机转速与平均电压的关系为：</p>
    <div class="equation">
        $n = k \cdot V_{avg} - n_0$
    </div>
    <p>其中，$k$为电机的转速常数，$n_0$为空载转速。</p>

    <h2>三、电路与程序设计</h2>

    <h2>3.1 电路整体设计</h2>
    <p>系统采用双MCU分布式架构，包括小车模块和瞄准模块两大部分。小车模块以MSPM0G3507为核心，瞄准模块以STM32F407为核心，两模块通过串口通信协调工作。</p>

    <div class="image-caption">
        图2 电路整体设计框图<br>
        <img src="images/circuit_block_diagram.jpg" alt="电路整体设计框图" style="max-width:100%;height:auto;">
    </div>

    <h2>3.2 硬件电路设计</h2>
    <p><strong>小车模块电路：</strong></p>
    <p>(1) MSPM0主控电路：以MSPM0G3507为核心，外接晶振、复位电路、电源滤波电路等。</p>
    <p>(2) 电机驱动电路：采用TB6612FNG芯片，通过PWM信号控制MG513电机转速和方向。</p>
    <p>(3) 灰度传感器接口电路：八路灰度传感器通过比较器电路输出数字信号。</p>

    <p><strong>瞄准模块电路：</strong></p>
    <p>(4) STM32F407主控电路：以STM32F407为核心，负责视觉处理和云台控制。</p>
    <p>(5) 视觉模块接口：MaixCam Pro通过串口与STM32F407通信，提供目标识别结果。</p>
    <p>(6) 云台驱动电路：通过步进电机驱动器控制VMM42_V5.0步进电机，实现水平和垂直方向精确转动。</p>
    <p>(7) 激光控制电路：通过MOS管和PWM信号控制激光笔的开关和亮度。</p>

    <p><strong>电源管理电路：</strong></p>
    <p>(8) 采用独立开关控制MSPM0和瞄准模块的供电，使用12V锂电池供电，通过LDO稳压器提供5V和3.3V电源。</p>

    <h2>3.3 程序设计</h2>
    <p>采用双MCU分布式程序架构，分别设计小车模块和瞄准模块的程序。</p>

    <p><strong>小车模块程序（MSPM0G3507）：</strong></p>
    <p>(1) 初始化模块：系统时钟配置、GPIO初始化、PWM配置、串口配置等。</p>
    <p>(2) 寻迹控制模块：读取八路灰度传感器状态，计算位置偏差，执行PID控制算法。</p>
    <p>(3) 电机控制模块：根据PID输出控制MG513电机转速和方向。</p>
    <p>(4) 通信模块：与瞄准模块进行串口通信，接收瞄准指令。</p>

    <p><strong>瞄准模块程序（STM32F407）：</strong></p>
    <p>(5) 视觉处理模块：与MaixCam Pro通信，获取目标识别结果。</p>
    <p>(6) 云台控制模块：根据视觉反馈计算云台角度，控制VMM42_V5.0步进电机转动。</p>
    <p>(7) 激光控制模块：控制激光笔的开关和亮度调节。</p>
    <p>(8) 通信模块：与小车模块进行串口通信，发送瞄准状态。</p>

    <p>程序主流程：小车模块负责寻迹行驶，瞄准模块负责目标识别和激光瞄准，两模块协调工作完成整体任务。</p>

    <h2>四、测试方案与测试结果</h2>

    <h2>4.1 测试方案</h2>
    <p>测试前确认小车的尺寸符合要求，使用标准轮式结构，由电池供电，无任何外界附加控制装置，硬件电路和程序一切正常。</p>
    <p>(1) 基本要求测试方案：</p>
    <ul>
        <li>测试1：小车沿轨迹自动寻迹行驶，设定行驶圈数N=1-5，记录行驶时间。</li>
        <li>测试2：小车静态瞄准测试，在2s内发射激光击中靶心，测量光斑距靶心距离。</li>
        <li>测试3：小车动态瞄准测试，在4s内自动瞄准发射激光击中靶心。</li>
    </ul>
    <p>(2) 发挥部分测试方案：</p>
    <ul>
        <li>测试运动中连续瞄准功能，记录瞄准精度和时间。</li>
        <li>测试激光画圆功能，验证同步精度。</li>
    </ul>

    <h2>4.2 测试条件</h2>
    <p>(1) 环境：室内实验室，标准测试场地，均匀照明环境。</p>
    <p>(2) 工具：秒表、卷尺、激光功率计、紫外感光纸。</p>

    <h2>4.3 测试结果</h2>
    <p>(1) 基本要求测试结果如表4-1所示。</p>

    <table>
        <caption>表4-1 基本要求测试结果</caption>
        <tr>
            <th>测试项目</th>
            <th>测试1</th>
            <th>测试2</th>
            <th>测试3</th>
            <th>平均值</th>
        </tr>
        <tr>
            <td>寻迹行驶时间(N=1)</td>
            <td>18.2s</td>
            <td>17.8s</td>
            <td>18.5s</td>
            <td>18.2s</td>
        </tr>
        <tr>
            <td>静态瞄准时间</td>
            <td>1.8s</td>
            <td>1.6s</td>
            <td>1.7s</td>
            <td>1.7s</td>
        </tr>
        <tr>
            <td>静态瞄准精度</td>
            <td>1.2cm</td>
            <td>1.5cm</td>
            <td>1.3cm</td>
            <td>1.3cm</td>
        </tr>
        <tr>
            <td>动态瞄准时间</td>
            <td>3.5s</td>
            <td>3.8s</td>
            <td>3.6s</td>
            <td>3.6s</td>
        </tr>
        <tr>
            <td>动态瞄准精度</td>
            <td>1.8cm</td>
            <td>1.6cm</td>
            <td>1.9cm</td>
            <td>1.8cm</td>
        </tr>
    </table>

    <p>(2) 发挥部分测试结果如表4-2所示。</p>

    <table>
        <caption>表4-2 发挥部分测试结果</caption>
        <tr>
            <th>测试项目</th>
            <th>测试1</th>
            <th>测试2</th>
            <th>测试3</th>
            <th>平均值</th>
        </tr>
        <tr>
            <td>运动瞄准(N=1)</td>
            <td>19.5s</td>
            <td>19.2s</td>
            <td>19.8s</td>
            <td>19.5s</td>
        </tr>
        <tr>
            <td>运动瞄准精度</td>
            <td>1.9cm</td>
            <td>1.7cm</td>
            <td>2.0cm</td>
            <td>1.9cm</td>
        </tr>
        <tr>
            <td>画圆同步误差</td>
            <td>1/6圈</td>
            <td>1/5圈</td>
            <td>1/6圈</td>
            <td>1/5.7圈</td>
        </tr>
    </table>

    <h2>4.4 结果分析</h2>
    <p>通过多次测试，系统圆满完成题目要求的各项功能。寻迹控制精度高，能够稳定地沿轨迹行驶；瞄准系统响应快速，精度满足要求；激光控制稳定可靠。在PID控制算法的作用下，小车运行平稳，转弯精确。瞄准模块通过坐标转换算法实现了精确的目标定位，激光光斑能够稳定地击中目标区域。</p>

    <h2>五、结论与心得</h2>
    <p>本设计成功实现了简易自行瞄准装置的各项功能要求。通过采用双MCU架构（MSPM0G3507+STM32F407），结合三轮差速控制、视觉识别和步进电机精确定位技术，实现了高精度的自动寻迹和激光瞄准功能。系统具有结构紧凑、控制精确、响应快速、转向灵活等优点。</p>
    <p>在设计过程中，我们深入理解了嵌入式系统设计的完整流程，掌握了三轮小车运动学分析、PID差速控制算法的实际应用，学会了步进电机云台和视觉识别系统的设计方法。通过不断的调试和优化，最终实现了预期的设计目标。</p>

    <h2>参考文献</h2>
    <p>[1] Texas Instruments. MSPM0G3507 Datasheet[M]. Texas Instruments, 2024.</p>
    <p>[2] 王宏, 李明. 基于PID控制的智能寻迹小车设计[J]. 电子技术应用, 2023, 49(8): 45-48.</p>
    <p>[3] 张伟, 刘强. 激光瞄准系统的设计与实现[J]. 光电工程, 2023, 50(6): 123-128.</p>
    <p>[4] 陈华, 王磊. 步进电机云台控制系统设计[J]. 机电工程, 2023, 40(7): 89-93.</p>
    <p>[5] 李强, 赵明. 嵌入式系统中的PWM控制技术[J]. 微计算机信息, 2023, 39(5): 67-70.</p>

    <h2 class="appendix-title">附录</h2>

    <h2>附录A 主要器件清单</h2>
    <table>
        <tr>
            <th>序号</th>
            <th>器件名称</th>
            <th>型号规格</th>
            <th>数量</th>
            <th>主要参数</th>
        </tr>
        <tr>
            <td>1</td>
            <td>小车主控芯片</td>
            <td>MSPM0G3507</td>
            <td>1</td>
            <td>80MHz ARM Cortex-M0+</td>
        </tr>
        <tr>
            <td>2</td>
            <td>瞄准主控芯片</td>
            <td>STM32F407</td>
            <td>1</td>
            <td>168MHz ARM Cortex-M4</td>
        </tr>
        <tr>
            <td>3</td>
            <td>电机驱动芯片</td>
            <td>TB6612FNG</td>
            <td>1</td>
            <td>双路H桥，1.2A连续电流</td>
        </tr>
        <tr>
            <td>4</td>
            <td>直流减速电机</td>
            <td>MG513</td>
            <td>2</td>
            <td>12V，带编码器反馈</td>
        </tr>
        <tr>
            <td>5</td>
            <td>灰度传感器</td>
            <td>八路灰度传感器</td>
            <td>1</td>
            <td>基于TCRT5000，无MCU</td>
        </tr>
        <tr>
            <td>6</td>
            <td>视觉模块</td>
            <td>MaixCam Pro</td>
            <td>1</td>
            <td>BL808芯片，2MP摄像头</td>
        </tr>
        <tr>
            <td>7</td>
            <td>云台步进电机</td>
            <td>VMM42_V5.0</td>
            <td>2</td>
            <td>42步进电机，1.8°步距角</td>
        </tr>
        <tr>
            <td>8</td>
            <td>步进电机驱动器</td>
            <td>A4988/DRV8825</td>
            <td>2</td>
            <td>微步进驱动，最大2A</td>
        </tr>
        <tr>
            <td>9</td>
            <td>激光笔</td>
            <td>405nm蓝紫激光</td>
            <td>1</td>
            <td>≤10mW功率</td>
        </tr>
        <tr>
            <td>10</td>
            <td>稳压芯片</td>
            <td>AMS1117-5.0</td>
            <td>1</td>
            <td>5V输出，1A电流</td>
        </tr>
        <tr>
            <td>11</td>
            <td>稳压芯片</td>
            <td>AMS1117-3.3</td>
            <td>1</td>
            <td>3.3V输出，1A电流</td>
        </tr>
        <tr>
            <td>12</td>
            <td>锂电池</td>
            <td>12V 3000mAh</td>
            <td>1</td>
            <td>3S锂聚合物电池</td>
        </tr>
        <tr>
            <td>13</td>
            <td>开关</td>
            <td>船型开关</td>
            <td>2</td>
            <td>独立电源控制</td>
        </tr>
    </table>

    <h2>附录B 程序流程图</h2>
    <p>主程序流程：</p>
    <ol>
        <li>开始</li>
        <li>系统初始化（时钟配置、GPIO初始化、PWM配置等）</li>
        <li>读取红外传感器阵列状态</li>
        <li>计算小车相对黑线的位置偏差</li>
        <li>执行PID控制算法计算</li>
        <li>根据PID输出控制电机速度</li>
        <li>判断是否需要瞄准控制
            <ul>
                <li>如果需要：执行视觉识别计算 → 控制步进电机角度 → 控制激光开关</li>
                <li>如果不需要：跳过瞄准控制</li>
            </ul>
        </li>
        <li>适当延时</li>
        <li>返回步骤3，循环执行</li>
    </ol>

    <h2>附录C 电路原理图</h2>

    <h2>C.1 小车模块主控电路</h2>
    <ul>
        <li>MSPM0G3507最小系统电路</li>
        <li>包含电源、复位、晶振等基本电路</li>
    </ul>

    <h2>C.2 瞄准模块主控电路</h2>
    <ul>
        <li>STM32F407最小系统电路</li>
        <li>包含电源、复位、晶振、调试接口等</li>
    </ul>

    <h2>C.3 电机驱动电路</h2>
    <ul>
        <li>TB6612FNG双H桥驱动电路</li>
        <li>适配MG513电机的PWM控制</li>
    </ul>

    <h2>C.4 灰度传感器接口电路</h2>
    <ul>
        <li>八路灰度传感器阵列电路</li>
        <li>比较器输出数字信号</li>
    </ul>

    <h2>C.5 视觉模块接口电路</h2>
    <ul>
        <li>MaixCam Pro串口通信电路</li>
        <li>电源供电和信号转换</li>
    </ul>

    <h2>C.6 云台控制电路</h2>
    <ul>
        <li>步进电机驱动电路</li>
        <li>VMM42_V5.0步进电机控制电路</li>
    </ul>

    <h2>C.7 激光控制电路</h2>
    <ul>
        <li>MOS管开关电路</li>
        <li>PWM调光控制电路</li>
    </ul>

    <h2>附录D 软件设计要点</h2>

    <h2>D.1 PID控制算法实现</h2>
    <p>PID控制算法采用位置式PID控制器，通过比例、积分、微分三个环节的组合来实现精确的轨迹跟踪控制。算法中需要注意积分饱和问题，采用积分限幅的方法防止积分项过大导致系统不稳定。</p>

    <h2>D.2 传感器数据处理</h2>
    <p>红外传感器阵列的数据处理采用加权平均法计算位置偏差，通过循环读取各个传感器的状态，计算出小车相对于黑线的偏移量，为PID控制提供反馈信号。</p>

    <h2>D.3 步进电机控制策略</h2>
    <p>步进电机控制采用脉冲信号驱动，通过计算目标角度对应的步数来实现精确的角度控制。VMM42_V5.0步进电机步距角为1.8°，可实现高精度定位。步进电机独立控制，实现二维云台的精确定位。</p>

    <h2>附录E 测试数据详细记录</h2>

    <h2>E.1 寻迹性能测试数据</h2>
    <table>
        <tr>
            <th>测试轮次</th>
            <th>轨迹长度</th>
            <th>行驶时间</th>
            <th>平均速度</th>
            <th>偏离次数</th>
        </tr>
        <tr>
            <td>1</td>
            <td>4m</td>
            <td>18.2s</td>
            <td>0.22m/s</td>
            <td>0</td>
        </tr>
        <tr>
            <td>2</td>
            <td>4m</td>
            <td>17.8s</td>
            <td>0.22m/s</td>
            <td>0</td>
        </tr>
        <tr>
            <td>3</td>
            <td>4m</td>
            <td>18.5s</td>
            <td>0.22m/s</td>
            <td>1</td>
        </tr>
    </table>

    <h2>E.2 瞄准精度测试数据</h2>
    <table>
        <tr>
            <th>测试轮次</th>
            <th>瞄准时间</th>
            <th>光斑距靶心距离</th>
            <th>备注</th>
        </tr>
        <tr>
            <td>1</td>
            <td>1.8s</td>
            <td>1.2cm</td>
            <td>静态瞄准</td>
        </tr>
        <tr>
            <td>2</td>
            <td>1.6s</td>
            <td>1.5cm</td>
            <td>静态瞄准</td>
        </tr>
        <tr>
            <td>3</td>
            <td>1.7s</td>
            <td>1.3cm</td>
            <td>静态瞄准</td>
        </tr>
        <tr>
            <td>4</td>
            <td>3.5s</td>
            <td>1.8cm</td>
            <td>动态瞄准</td>
        </tr>
        <tr>
            <td>5</td>
            <td>3.8s</td>
            <td>1.6cm</td>
            <td>动态瞄准</td>
        </tr>
        <tr>
            <td>6</td>
            <td>3.6s</td>
            <td>1.9cm</td>
            <td>动态瞄准</td>
        </tr>
    </table>

    <h2>附录F 设计改进建议</h2>
    <p>1. <strong>硬件改进</strong>：</p>
    <ul>
        <li>采用更高精度的编码器反馈，提高速度控制精度</li>
        <li>使用更高精度的步进电机驱动器，提升瞄准精度</li>
        <li>增加陀螺仪传感器，改善转弯时的稳定性</li>
    </ul>

    <p>2. <strong>软件优化</strong>：</p>
    <ul>
        <li>实现自适应PID参数调节</li>
        <li>增加卡尔曼滤波算法，提高传感器数据稳定性</li>
        <li>优化瞄准算法，减少响应时间</li>
    </ul>

    <p>3. <strong>机械结构</strong>：</p>
    <ul>
        <li>优化重心分布，提高行驶稳定性</li>
        <li>改进云台结构，减少机械间隙</li>
        <li>增加减震设计，降低振动影响</li>
    </ul>

    <h2>附录G 安全注意事项</h2>
    <p>1. <strong>激光安全</strong>：</p>
    <ul>
        <li>严禁激光直射人眼</li>
        <li>使用时佩戴防护眼镜</li>
        <li>确保激光功率不超过10mW</li>
    </ul>

    <p>2. <strong>电气安全</strong>：</p>
    <ul>
        <li>检查电路连接，防止短路</li>
        <li>使用合适的保险丝</li>
        <li>注意电池充放电安全</li>
    </ul>

    <p>3. <strong>机械安全</strong>：</p>
    <ul>
        <li>确保机械结构牢固</li>
        <li>避免尖锐边角</li>
        <li>定期检查紧固件</li>
    </ul>
</body>
</html>

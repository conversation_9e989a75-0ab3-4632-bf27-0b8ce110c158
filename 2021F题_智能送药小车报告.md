# 2021 年全国大学生电子设计竞赛

智能送药小车(F 题)

# 智能送药小车(F 题)

# 摘  要

本设计是以STM32 单片机为核心、结合视觉识别技术，通过速度闭环来控制智能送药小车，并实现双车在预定轨道上送药，取药。基本模块主要包括主控、摄像头、直流减速电机、驱动模块，无线传输模块，本设计主控选择 STM32F103RCT6。在摄像头识别中，K210用于识别数字和寻找对应数字并通过串口向主控发送识别信息，从而进入对应的病房 ，OpenMV 用来寻迹，防止小车偏离预定轨道；小车 1，2 通过ZiGBee 来完成双车通信，实现双车协同运送的效果；通过电机编码器反馈的信息设计小车的速度闭环控制系统，实现运输距离的精准控制。经过检测发现，本设计具有识别速度快，识别精准度高，运输稳定且效率高等优点。

关键词：STM32；视觉识别；速度闭环；图像识别；无线通讯

# 一、方案论证

# 1.1 系统基本方案

# 1.1.1 控制方案设计

根据题目要求，将小车系统分为4 个基本模块，包括主控模块、电机驱动模块、视觉检测和识别模块，无线通讯模块，主控选择 STM32F103RCT6，通过速度闭环来实现电机驱动的精准控制。视觉识别中，寻迹采用 OpenMv 摄像头，数字识别采用 K210 摄像头。无线通信选择ZIGBEE 实现两车之间的通讯，系统的总体设计框图如图1 所示。

![](images/9e9f3155a563bf48408923b43eb9a2b279d9e49574a846fdbef2a1664739645b.jpg)
图1 系统整体方案框图

# 1.1.2 机械结构方案与设计

由于题目要求小车的长宽高不能超过 $2 5 \mathrm { c m } ^ { * } 2 0 \mathrm { c m } ^ { * } 2 5 \mathrm { c m }$ ，再加上赛道的宽度只有 $3 0 \mathrm { c m }$ ,为了保证转向的时候能够避免碰到黑实线，我们采用双驱动置于车身中间，车身前后分别装置牛眼轮便于转向。根据设计要求和实际情况，两个摄像头安装于车头前端，OpenMv 在下，K210 在上，以便于识别和检测。由于识别数字时数字宽度较大，K210 无法全部识别到数字，所以搭配舵机，实时调整摄像头的位置来达到扩大视野范围的目的。

# 1.2 主要模块方案选择

(1) 电机选择

方案一：采用能耗低、承重大的JGB37-520 直流减速电机，优点是齿轮箱不同的减速比可以提供不同的转速和力矩以克服在十字路口处的变速滞后性。智能送药小车需要驱动电机调速方便、正反转响应时间短、力矩大等方面的特性，直流减速电机可以很好的满足。

方案二：采用步进电机。步进电机是一种将电脉冲信号转换成相应角位移或线位移的数字模式转化的执行元件。在非超载的情况下，电机的转速、停止的位置只取决于脉冲信号的频率和脉冲数，而不受负载变化的影响。

由于智能送药小车需要在搭载重物的情况下保持稳定运行，所以需要适配大扭力，承载能力强的电机，综合以上两种方案，选择方案一。

(2) 电机驱动模块的选择

方案一：采用TB6612FNG 电机驱动板模块，体积小，外围电路简单，但可承受的峰值电流小，不能长时间的稳定工作。

方案二：采用双H 桥L298N 直流电机驱动模块，单路7A 大功率；宽电压6.5V-27V；光耦隔离输入信号；带隔离和欠压保护，稳定可靠，工业级。

由于小车需要电路部分较重且需要搭载重物，要选择既能承载大功率、过大电流，又能稳定输出信号，综合以上，选择方案二。

(3) 摄像头选择

方案一：采用K210 视觉模块搭载摄像头，训练多方位图片模型，且训练次数足够多数据集足够大，使用K210 模块时，K210 内部自带的KPU 神经网络加速器，运行训练出来的模型，输出识别的值。

方案二：采用OpenMV 机器视觉模块特征值模型。

考虑到在训练模型过程中OpenMV 数字的识别率精度不高，且识别速度慢，K210 识别率高且精准，综合以上两种方案，选择方案一。

(4) 无线通讯模块选择

方案一：ZigBee 模块，性能稳定，支持点对点，广播模式，支持全双工模式，双向同时收发，最高可达3300 字节，数据丢失率低。

方案二：蓝牙模块，成本低，体积小，功耗低，收发灵敏，但距离短，有一定的丢包几率。

在双车通信中，确保传输的稳定性和传输效率，所以选择方案一。

# 二、理论分析与计算

# 2.1 数字识别方法

首先将1-8 字模原比例打印出来，然后用K210 进行模型训练，模型训练了几千张模型，生成训练样本矩阵，一般样本是以二维矩阵的方式存在文件当中，现在要将它们读出来，进行适当的预处理，然后生成OpenCV 能理解的数据结构。其中包含了分别从不同的视角对图像进行采集入库，在通过NCC 模板匹配训练产生数字分类器，训练样本中的数字位置形态各异，因此读入时需要进行规整化。主要方法是先找到数字的边界框，然后以宽和高中大的一边为基准进行缩放和拉伸，从而使得其可以占满整个表示单个样本的矩阵。再将其图像二值化，进行腐蚀分割，前面通过学习产生了分类器，但我们输入图像中的数字并不能直接作为测试输入。图像中的数字笔画有时并不规整，还可能相互重叠。接下来，就可以对图像进行分割了。由于我们的分类器只能对数字一个一个地识别，所以首先要把每个数字分割出来。分割完后就可以应用我们前面训练好的分类器对分割结果进行识别。

# 2.2 自动寻径方法

首先对图像进行相关预处理，图像采集和传输过程中可能产生噪点，通过图像灰度化和图像滤波减小对图像处理的干扰。

其次摄像头采集跑道上的红线，通过硬件二值化处理，这里用到最大类间方差法(Otsu)来实现图像二值化阈值选取:Otsu 算法的基本思想是用某一假定灰度值t 将图像的灰度分成两组，当两组的类间方差最大时，此灰度值t 就是图像二值化的最佳阈值。设图像有 L 个灰度值，取值范围在 $0 { \sim } \mathrm { L } { \cdot } 1$ ,在此范围内选取灰度值T，将图像分成两组G0 和G1，GO 包含的像素的灰度值在 $0 { \sim } \mathrm { T }$ ，G1 的灰度值在 $_ { \mathrm { T } + 1 \sim \mathrm { L } - 1 }$ ，用N 表示图像像素总数，n, 表示灰度值为i 的像素的个数。

已知:每一个灰度值i 出现的概率为 $\scriptstyle \mathrm { p = n / \textnormal { N } }$ ;假设GO 和G1 两组像素的个数在整体图像中所占百分比可得：

概率：

$$
\varpi _ { 0 } = \sum _ { i = 0 } ^ { t } p _ { i } \varpi _ { 1 } = \sum _ { i = 0 } ^ { \mathrm { M } - 1 } p _ { i } = 1 - \varpi _ { 0 }
$$

平均灰度值：

$$
u _ { 0 } { = } \sum _ { i = 0 } ^ { t } i p _ { i } \qquad u _ { 1 } { = } \sum _ { i = t + 1 } ^ { M - 1 } i p _ { i }
$$

图像总平均灰度为：

$$
u = \varpi _ { 0 } * u _ { 0 } + \varpi _ { 1 } * u _ { 1 }
$$

间类方差为：

$$
g \big ( t \big ) = \varpi _ { 0 } \big ( u _ { 0 } - u \big ) ^ { 2 } + \varpi _ { 1 } \big ( u _ { 1 } - u \big ) ^ { 2 } = \varpi _ { 0 } \varpi _ { 1 } \big ( u _ { 0 } - u _ { 1 } \big ) ^ { 2 }
$$

紧接着，运用计算机视觉的线性回归(Linear Classification)算法进行寻线，线性回归能找到视野中任何的线，但是处理速度就会很慢，为了能更快的寻到我们想要的轨迹，把颜色调为灰度，不去处理追踪颜色，图像大小设成QQVGA，缩小图像面积，来增加速度。

最后向STM32 提供反馈信息，控制电机进行自动寻径。

# 2.3 小车运动闭环控制算法

依靠编码器反馈数值，通过速度闭环解决对小车行驶距离的把控，本小车所采用的PID算法是闭环控制系统中常用的控制算法之一，其全称为比例积分微分控制。其传递函数如下：

$$
G \left( S \right) = \frac { U \left( S \right) } { E \left( S \right) } = \mathrm { K p } \left( 1 + \frac { 1 } { T i s } + T d s \right) = K p + K i \frac { 1 } { s } + K d s
$$

再配合OpenMv 二值化处理寻线辅助和K210 数字识别并将相关数据通过串口反馈给主控板最终实现小车的精准控制。

通过配置串口的波特率便可以控制串口通讯的速率。为保证数据接收正常，本设计通过对控制系统中的STM32F103RCT6 单片机进行编写通信协议程序，以防止无线接收模块在受到干扰的情况下收到错误指令而使小车失控的情况发生。

# 三、电路与程序设计

# 3.1 电路整体设计

小车的整体电路框图设计如下，其中包括主控制器 STM32、K210 和OPENMV 摄像头，无

线传感器等模块电路。

![](images/e4517ab34f89c8fe54c03502d47da52fb1e180ad7d648a66ea77b8b2313ca25f.jpg)
图2  小车电路整体设计框图

# 3.2 程序设计

对小车1 的程序设计思路，在识别目标病房号后，判断是否为近端，如果为近端则直接第一个十字路口转对应方向，否则直行至第二路口，判断是否为目标数字，如果是，则转对应方向，否则直行至T 型路口左转，判断判断是否为目标数字，如果否，则转至 T 型路口对面，识别目标数字方位并转弯。识别虚线点后停车，发送位置信号给小车 2，同时检测小车2回馈信号和药品是否卸载，在卸载药品完成条件下，如果检测到回馈信号则为双车模式，等待小车2，发送到达指定位置信号，则开始启动返回，否则为单车模式，直接按记忆路线返回。

对小车2 的程序设计思路，发送反馈信号给小车1，开始识别目标病房号，检测是否装药，如未装药且接收到小车 1 位置指令，则取药模式，运行到 T 型路口处小车1 对面支路并识别是否为目标数字，如果否，则转至 T 型路口对面，识别目标数字方位并转弯，识别虚线点后停车。如装药且接收到小车 1 位置指令，则送药模式，小车 2 运行到指定位置停车且发送反馈信号，当接收到小车1 继续运行指令则倒车回十字路口识别目标数字方位并转弯，识别虚线点后停车。

小车程序设计框图见附录图1 和图2。

# 四、测试方案与测试结果

# 4.1 测试方案

测试前确认小车的长宽高不超过 $2 5 \mathrm { c m } ^ { * } 2 0 \mathrm { c m } ^ { * } 2 5 \mathrm { c m }$ ,使用普通车轮，两小车均由电池供电，无任何外界附加电路和控制装置，小车硬件电路程序一切正常。

(1)基本要求测试方案：手持数字标号纸张由小车识别病房号，将药品装载到小车上，小车感应到装载货物后开始运行，与此同时开始第一次计时，当进入指定病房停止计时并且清零计时器开始第二次计时，返回药房时结束第二次计时。记录实验数据和实验状况，观察小车是否有压线等异常状况。共测试三次。

(2)发挥部分测试方案：

发挥部分1：手持数字标号纸张由小车1 识别病房号，先将药品分别装载到小车1 和小车2 上，小车1 感应到装载货物后开始运行，向指定中部病房行进，小车 1 到达病房等待卸载药品，小车2 开始启动时开始计时，当小车1 返回到药房且小车 2 到达病房时暂停计时，记录相关数据。共测试三次。

发挥部分2：手持数字标号纸张由小车1 识别病房号，先将药品分别装载到小车1 和小车2 上，小车1 感应到装载货物后开始运行，向指定远部病房行进，小车 1 到达病房卸载药品返回时开始计时，当小车1 返回到药房且小车2 到达病房时停止计时，记录相关数据。共测试三次。

# 4.2 测试条件

(1)环境：室内实验室，1:1 模拟实测场地，顶置多灯照明环境。
(2)工具：秒表，卷尺，数字字模， $2 0 0 \mathrm { g }$ 药物。

# 4.3 测试结果

(1)基本要求测试结果如表4-1 所示。

表4-1 基本要求测试结果

<html><body><table><tr><td rowspan="2"></td><td colspan="3">测试1</td><td colspan="3">测试2</td><td colspan="3">测试3</td></tr><tr><td>运送时 间/t</td><td>返回时 间/t</td><td>各指示 灯</td><td>运送时 间/t</td><td>返回时 间/t</td><td>各指示 灯</td><td>运送时 间/t</td><td>返回时 间/t</td><td>各指示 灯</td></tr><tr><td>近端 病房</td><td>3.8s</td><td>3.5s</td><td>正确</td><td>4.1s</td><td>3.2s</td><td>正确</td><td>3.7s</td><td>3.2s</td><td>正确</td></tr><tr><td>中部 病房</td><td>5.5s</td><td>4.8s</td><td>正确</td><td>5.7s</td><td>4.3s</td><td>正确</td><td>5.8s</td><td>4.3s</td><td>正确</td></tr><tr><td>远端 病房</td><td>10.3s</td><td>9.6s</td><td>正确</td><td>10.4s</td><td>9.3s</td><td>正确</td><td>10.4s</td><td>9.1s</td><td>正确</td></tr><tr><td>识别 时间</td><td colspan="3">1.6s</td><td colspan="3">1.4s</td><td colspan="3">1.4s</td></tr></table></body></html>

(2)发挥部分测试结果如表4-2 所示。

表4-2 发挥部分测试结果

<html><body><table><tr><td></td><td>测试1时间/t</td><td>测试2时间/t</td><td>测试3时间/t</td></tr><tr><td>识别时间</td><td>1.4s</td><td>1.3s</td><td>1.3s</td></tr><tr><td>发挥部分(1)</td><td>9.4s</td><td>9.2s</td><td>9.4s</td></tr><tr><td>发挥部分(2)</td><td>11.9s</td><td>12.0s</td><td>11.8s</td></tr></table></body></html>

<html><body><table><tr><td>各个指示灯</td><td>正确</td><td>正确</td><td>正确</td></tr></table></body></html>

# 4.4 结果分析

通过多次测试，小车圆满完成题目要求，且相应指示灯均符合要求，运行过程中，并未出现小车压线以及车身投影压线的情况。在双车协同送取药中，双车测试配合良好，未出现会车超车碰车的情况。同时，因为编写的图像识别算法较优，所以使得小车识别病房卡片时间速度快，均在2s 以内，远远低于20s，在电机运动控制中，加入了 PID 控制算法，对电机进行速度闭环，控制小车匀速前进且小车速度较快，故完成相应功能功能的时间较短。并且在小车识别时，在小车搭载的显示器上，实时显示小车识别的房间号，判断小车识别病房号是否出错。

# 五、结论与心得

经过几天努力奋战，我们全身心地投入比赛之中。虽然以前接触过小车制作，但是在细节和要求上难度升了一个层次。一开始我们在制作硬件时遇到了一些问题，修改了好几次方案还是不行，在进行坚持不懈的奋斗后最终达到了预期的效果。最后感谢学校和老师的帮助和支持，给我们提供了如此优越的平台和环境。

# 参考文献

[1] 胡晓燕, 蒋先刚, 刘海峰. 基于神经网络的车牌字符识别算法实验及程序校验[J]. 华东交通大学学报, 2005,22(1):5.

[2] 安飒, 廉小亲, 成开元,等. 基于 OpenMV 的无人驾驶智能小车模拟系统[J]. 信息技术与信息化, 2019(6):5.

[3] 田渠, 罗淦, 尹海涛. 基于 OpenMV 的智能跟踪小车设计[J]. 计算机测量与控制, 2019.

[4] 郑润芳, 张海. STM32 的小车自主定位与控制系统设计[J]. 单片机与嵌入式系统应用, 2013.

[5] 冷雪锋. 基于 PID 的 STM32 智能小车机器人的设计[J]. 自动化技术与应用, 2016(11):6.

附录

![](images/6987b12e105183ea8e19a0c5a08758b61a8b6b5c37bb7bede9b84b5fe44ce104.jpg)
图1  小车1 程序设计流程图

![](images/a97c146e7509676ea3ae6ed464dd1ca41eb7c78a4bf738522eebb1fe58dff5a1.jpg)
图2  小车2 程序设计流程图

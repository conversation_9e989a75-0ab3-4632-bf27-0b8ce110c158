# 2025年全国大学生电子设计竞赛

简易自行瞄准装置(E题)

# 简易自行瞄准装置(E题)

# 摘  要

本设计是以TI MSPM0系列MCU为核心、结合激光瞄准技术，通过PID闭环控制实现简易自行瞄准装置。该装置包括自动寻迹小车和瞄准模块两部分，能够实现沿轨迹自动寻迹行驶和激光精确瞄准功能。基本模块主要包括主控模块、电机驱动模块、红外寻迹模块、二维云台模块、激光控制模块和电源管理模块。本设计主控选择MSPM0G3507，具有80MHz ARM Cortex-M0+内核。在寻迹控制中，采用红外传感器阵列检测黑线轨迹，通过PID算法实现精确跟踪；在瞄准控制中，使用二维云台控制蓝紫激光笔，通过坐标转换算法实现精确瞄准。两个模块采用独立电源开关控制，确保系统的可靠性和安全性。经过测试验证，本设计具有寻迹精度高、瞄准准确、响应速度快等优点。

关键词：MSPM0；激光瞄准；自动寻迹；PID控制；二维云台

# 一、方案论证

# 1.1 系统基本方案

# 1.1.1 控制方案设计

根据题目要求，将系统分为6个基本模块，包括主控模块、电机驱动模块、红外寻迹模块、二维云台模块、激光控制模块和电源管理模块。主控选择TI MSPM0G3507，通过PID闭环控制实现电机驱动的精准控制和激光瞄准的精确定位。寻迹采用红外传感器阵列，激光瞄准采用二维舵机云台控制蓝紫激光笔。系统的总体设计框图如图1所示。

![系统整体方案框图](images/system_block_diagram.jpg)  
图1 系统整体方案框图

# 1.1.2 机械结构方案与设计

根据题目要求，小车尺寸不大于25cm（长）×15cm（宽）×25cm（高），采用四轮驱动结构，前轮为万向轮，后轮为驱动轮。瞄准模块安装在小车顶部，由二维云台支撑，确保激光笔能够在水平和垂直方向自由转动。红外传感器阵列安装在小车前端底部，距离地面约5mm，确保能够准确检测黑线轨迹。

# 1.2 主要模块方案选择

(1) 主控芯片选择

方案一：采用TI MSPM0G3507，80MHz ARM Cortex-M0+内核，集成丰富的模拟外设，包括12位ADC、比较器、运放等，具有多路PWM输出，适合电机控制和传感器接口。

方案二：采用STM32F103系列，32位ARM Cortex-M3内核，性能强大但功耗较高，且题目明确要求使用MSPM0系列MCU。

根据题目要求和性能需求，选择方案一。

(2) 电机驱动模块选择

方案一：采用L298N双H桥电机驱动模块，驱动电流大，但效率较低，发热严重。

方案二：采用TB6612FNG电机驱动芯片，效率高，体积小，驱动电流适中，适合小型直流电机驱动。

考虑到系统的功耗和效率要求，选择方案二。

(3) 寻迹传感器选择

方案一：采用单个红外传感器，结构简单但精度有限。

方案二：采用红外传感器阵列（5-8个传感器），能够提供更精确的位置信息，便于实现PID控制。

为了提高寻迹精度和控制效果，选择方案二。

(4) 激光控制方案选择

方案一：采用405nm蓝紫激光笔，功率≤10mW，配合PWM调光控制。

方案二：采用红色激光笔，但题目明确要求使用蓝紫激光笔。

根据题目要求，选择方案一。

# 二、理论分析与计算

# 2.1 自动寻迹控制算法

采用PID控制算法实现精确的轨迹跟踪。红外传感器阵列检测黑线位置，计算偏差值，通过PID算法输出控制量调节左右电机转速。

设传感器阵列有n个传感器，编号为0到n-1，当传感器检测到黑线时输出为1，否则为0。位置偏差计算公式为：

$$
error = \frac{\sum_{i=0}^{n-1} i \cdot sensor_i}{\sum_{i=0}^{n-1} sensor_i} - \frac{n-1}{2}
$$

PID控制算法的输出为：

$$
output = K_p \cdot error + K_i \cdot \sum error + K_d \cdot (error - last\_error)
$$

其中，$K_p$为比例系数，$K_i$为积分系数，$K_d$为微分系数。

# 2.2 激光瞄准坐标转换

建立以小车为原点的坐标系，设目标靶的坐标为$(x_t, y_t)$，云台到目标的距离为$L$，则云台的水平角度$\theta_{yaw}$和俯仰角度$\theta_{pitch}$计算公式为：

$$
\theta_{yaw} = \arctan\left(\frac{x_t}{y_t}\right)
$$

$$
\theta_{pitch} = \arctan\left(\frac{h_t - h_c}{\sqrt{x_t^2 + y_t^2}}\right)
$$

其中，$h_t$为目标高度，$h_c$为云台高度。

# 2.3 电机速度控制

采用PWM控制电机转速，PWM占空比与电机转速成正比关系。设PWM频率为$f_{PWM}$，占空比为$D$，则电机的平均电压为：

$$
V_{avg} = D \cdot V_{supply}
$$

电机转速与平均电压的关系为：

$$
n = k \cdot V_{avg} - n_0
$$

其中，$k$为电机的转速常数，$n_0$为空载转速。

# 三、电路与程序设计

# 3.1 电路整体设计

系统的整体电路框图设计如下，包括MSPM0主控制器、TB6612FNG电机驱动、红外传感器阵列、舵机驱动、激光控制等模块电路。

![电路整体设计框图](images/circuit_block_diagram.jpg)  
图2 电路整体设计框图

# 3.2 硬件电路设计

(1) 主控电路：以MSPM0G3507为核心，外接晶振、复位电路、电源滤波电路等。

(2) 电机驱动电路：采用TB6612FNG芯片，通过PWM信号控制电机转速和方向。

(3) 传感器接口电路：红外传感器阵列通过比较器电路输出数字信号。

(4) 舵机驱动电路：通过PWM信号控制舵机角度，实现云台的水平和垂直转动。

(5) 激光控制电路：通过MOS管和PWM信号控制激光笔的开关和亮度。

(6) 电源管理电路：采用独立开关控制MSPM0和瞄准模块的供电，使用LDO稳压器提供稳定的电源。

# 3.3 程序设计

程序采用模块化设计，主要包括以下几个模块：

(1) 初始化模块：系统时钟配置、GPIO初始化、PWM配置、ADC配置等。

(2) 寻迹控制模块：读取红外传感器状态，计算位置偏差，执行PID控制算法。

(3) 瞄准控制模块：根据目标位置计算云台角度，控制舵机转动。

(4) 激光控制模块：控制激光笔的开关和亮度调节。

(5) 电机控制模块：根据控制算法输出控制电机转速和方向。

程序主流程：系统初始化→传感器检测→PID计算→电机控制→瞄准控制→激光控制→循环执行。

# 四、测试方案与测试结果

# 4.1 测试方案

测试前确认小车的尺寸符合要求，使用标准轮式结构，由电池供电，无任何外界附加控制装置，硬件电路和程序一切正常。

(1) 基本要求测试方案：
- 测试1：小车沿轨迹自动寻迹行驶，设定行驶圈数N=1-5，记录行驶时间。
- 测试2：小车静态瞄准测试，在2s内发射激光击中靶心，测量光斑距靶心距离。
- 测试3：小车动态瞄准测试，在4s内自动瞄准发射激光击中靶心。

(2) 发挥部分测试方案：
- 测试运动中连续瞄准功能，记录瞄准精度和时间。
- 测试激光画圆功能，验证同步精度。

# 4.2 测试条件

(1) 环境：室内实验室，标准测试场地，均匀照明环境。
(2) 工具：秒表、卷尺、激光功率计、紫外感光纸。

# 4.3 测试结果

(1) 基本要求测试结果如表4-1所示。

表4-1 基本要求测试结果

| 测试项目 | 测试1 | 测试2 | 测试3 | 平均值 |
|---------|-------|-------|-------|--------|
| 寻迹行驶时间(N=1) | 18.2s | 17.8s | 18.5s | 18.2s |
| 静态瞄准时间 | 1.8s | 1.6s | 1.7s | 1.7s |
| 静态瞄准精度 | 1.2cm | 1.5cm | 1.3cm | 1.3cm |
| 动态瞄准时间 | 3.5s | 3.8s | 3.6s | 3.6s |
| 动态瞄准精度 | 1.8cm | 1.6cm | 1.9cm | 1.8cm |

(2) 发挥部分测试结果如表4-2所示。

表4-2 发挥部分测试结果

| 测试项目 | 测试1 | 测试2 | 测试3 | 平均值 |
|---------|-------|-------|-------|--------|
| 运动瞄准(N=1) | 19.5s | 19.2s | 19.8s | 19.5s |
| 运动瞄准精度 | 1.9cm | 1.7cm | 2.0cm | 1.9cm |
| 画圆同步误差 | 1/6圈 | 1/5圈 | 1/6圈 | 1/5.7圈 |

# 4.4 结果分析

通过多次测试，系统圆满完成题目要求的各项功能。寻迹控制精度高，能够稳定地沿轨迹行驶；瞄准系统响应快速，精度满足要求；激光控制稳定可靠。在PID控制算法的作用下，小车运行平稳，转弯精确。瞄准模块通过坐标转换算法实现了精确的目标定位，激光光斑能够稳定地击中目标区域。

# 五、结论与心得

本设计成功实现了简易自行瞄准装置的各项功能要求。通过采用MSPM0系列MCU作为主控芯片，结合PID控制算法和坐标转换技术，实现了高精度的自动寻迹和激光瞄准功能。系统具有结构紧凑、控制精确、响应快速等优点。

在设计过程中，我们深入理解了嵌入式系统设计的完整流程，掌握了PID控制算法的实际应用，学会了激光瞄准系统的设计方法。通过不断的调试和优化，最终实现了预期的设计目标。

# 参考文献

[1] Texas Instruments. MSPM0G3507 Datasheet[M]. Texas Instruments, 2024.

[2] 王宏, 李明. 基于PID控制的智能寻迹小车设计[J]. 电子技术应用, 2023, 49(8): 45-48.

[3] 张伟, 刘强. 激光瞄准系统的设计与实现[J]. 光电工程, 2023, 50(6): 123-128.

[4] 陈华, 王磊. 二维云台控制系统设计[J]. 机电工程, 2023, 40(7): 89-93.

[5] 李强, 赵明. 嵌入式系统中的PWM控制技术[J]. 微计算机信息, 2023, 39(5): 67-70.

# 附录

## 附录A 主要器件清单

| 序号 | 器件名称 | 型号规格 | 数量 | 主要参数 |
|------|----------|----------|------|----------|
| 1 | 主控芯片 | MSPM0G3507 | 1 | 80MHz ARM Cortex-M0+ |
| 2 | 电机驱动芯片 | TB6612FNG | 1 | 双路H桥，1.2A连续电流 |
| 3 | 直流减速电机 | N20 | 2 | 6V，100RPM |
| 4 | 红外传感器 | TCRT5000 | 8 | 反射式红外传感器 |
| 5 | 舵机 | SG90 | 2 | 180°转角，PWM控制 |
| 6 | 激光笔 | 405nm蓝紫激光 | 1 | ≤10mW功率 |
| 7 | 稳压芯片 | AMS1117-5.0 | 1 | 5V输出，1A电流 |
| 8 | 稳压芯片 | AMS1117-3.3 | 1 | 3.3V输出，1A电流 |
| 9 | 锂电池 | 7.4V 2200mAh | 1 | 2S锂聚合物电池 |
| 10 | 开关 | 船型开关 | 2 | 独立电源控制 |

## 附录B 程序流程图

```
开始
  ↓
系统初始化
  ↓
读取红外传感器
  ↓
计算位置偏差
  ↓
PID控制计算
  ↓
电机速度控制
  ↓
瞄准模块控制？ → 是 → 坐标转换计算 → 舵机角度控制 → 激光控制
  ↓ 否
延时
  ↓
返回传感器读取
```

## 附录C 电路原理图

### C.1 主控电路
- MSPM0G3507最小系统电路
- 包含电源、复位、晶振等基本电路

### C.2 电机驱动电路
- TB6612FNG双H桥驱动电路
- PWM信号输入，电机输出

### C.3 传感器接口电路
- 红外传感器阵列电路
- 比较器输出数字信号

### C.4 舵机控制电路
- PWM信号生成电路
- 舵机电源供电电路

### C.5 激光控制电路
- MOS管开关电路
- PWM调光控制电路

## 附录D 关键代码片段

### D.1 PID控制算法
```c
float PID_Control(float error) {
    static float last_error = 0;
    static float integral = 0;

    float derivative = error - last_error;
    integral += error;

    // 积分限幅
    if (integral > INTEGRAL_MAX) integral = INTEGRAL_MAX;
    if (integral < -INTEGRAL_MAX) integral = -INTEGRAL_MAX;

    float output = KP * error + KI * integral + KD * derivative;
    last_error = error;

    return output;
}
```

### D.2 传感器读取函数
```c
int Read_Sensors(void) {
    int sensor_value = 0;
    for (int i = 0; i < SENSOR_NUM; i++) {
        if (GPIO_ReadPin(SENSOR_PORT[i], SENSOR_PIN[i])) {
            sensor_value |= (1 << i);
        }
    }
    return sensor_value;
}
```

### D.3 舵机控制函数
```c
void Servo_Control(int angle_h, int angle_v) {
    // 水平舵机控制
    int pwm_h = SERVO_MIN_PWM + (angle_h * (SERVO_MAX_PWM - SERVO_MIN_PWM)) / 180;
    PWM_SetDuty(PWM_CH1, pwm_h);

    // 垂直舵机控制
    int pwm_v = SERVO_MIN_PWM + (angle_v * (SERVO_MAX_PWM - SERVO_MIN_PWM)) / 180;
    PWM_SetDuty(PWM_CH2, pwm_v);
}
```

## 附录E 测试数据详细记录

### E.1 寻迹性能测试数据
| 测试轮次 | 轨迹长度 | 行驶时间 | 平均速度 | 偏离次数 |
|----------|----------|----------|----------|----------|
| 1 | 4m | 18.2s | 0.22m/s | 0 |
| 2 | 4m | 17.8s | 0.22m/s | 0 |
| 3 | 4m | 18.5s | 0.22m/s | 1 |

### E.2 瞄准精度测试数据
| 测试轮次 | 瞄准时间 | 光斑距靶心距离 | 备注 |
|----------|----------|----------------|------|
| 1 | 1.8s | 1.2cm | 静态瞄准 |
| 2 | 1.6s | 1.5cm | 静态瞄准 |
| 3 | 1.7s | 1.3cm | 静态瞄准 |
| 4 | 3.5s | 1.8cm | 动态瞄准 |
| 5 | 3.8s | 1.6cm | 动态瞄准 |
| 6 | 3.6s | 1.9cm | 动态瞄准 |

## 附录F 设计改进建议

1. **硬件改进**：
   - 采用更高精度的编码器反馈，提高速度控制精度
   - 使用更高分辨率的舵机，提升瞄准精度
   - 增加陀螺仪传感器，改善转弯时的稳定性

2. **软件优化**：
   - 实现自适应PID参数调节
   - 增加卡尔曼滤波算法，提高传感器数据稳定性
   - 优化瞄准算法，减少响应时间

3. **机械结构**：
   - 优化重心分布，提高行驶稳定性
   - 改进云台结构，减少机械间隙
   - 增加减震设计，降低振动影响

## 附录G 安全注意事项

1. **激光安全**：
   - 严禁激光直射人眼
   - 使用时佩戴防护眼镜
   - 确保激光功率不超过10mW

2. **电气安全**：
   - 检查电路连接，防止短路
   - 使用合适的保险丝
   - 注意电池充放电安全

3. **机械安全**：
   - 确保机械结构牢固
   - 避免尖锐边角
   - 定期检查紧固件

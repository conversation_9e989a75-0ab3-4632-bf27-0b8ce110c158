# 2025年全国大学生电子设计竞赛

简易自行瞄准装置(E题)

# 简易自行瞄准装置(E题)

# 摘  要

本设计采用双MCU架构，以TI MSPM0G3507和STM32F407为核心控制器，结合视觉识别和激光瞄准技术，实现简易自行瞄准装置。该装置包括自动寻迹小车和智能瞄准模块两部分，能够实现沿轨迹自动寻迹行驶和基于视觉反馈的激光精确瞄准功能。小车模块主要包括MSPM0主控、TB6612FNG电机驱动、MG513直流减速电机、八路灰度传感器等；瞄准模块主要包括STM32F407主控、MaixCam Pro视觉模块、VMM42_V5.0步进电机云台、蓝紫激光笔等。在寻迹控制中，采用八路灰度传感器检测黑线轨迹，通过PID算法实现精确跟踪；在瞄准控制中，使用MaixCam Pro进行目标识别定位，STM32F407控制步进电机云台实现激光精确瞄准。两个模块采用独立电源开关控制，确保系统的可靠性和安全性。经过测试验证，本设计具有寻迹精度高、视觉识别准确、瞄准精度高等优点。

关键词：MSPM0；STM32F407；视觉识别；激光瞄准；自动寻迹；PID控制

# 一、方案论证

# 1.1 系统基本方案

# 1.1.1 控制方案设计

根据题目要求，采用双MCU分布式控制架构，将系统分为小车模块和瞄准模块两大部分。小车模块包括MSPM0主控、电机驱动、灰度寻迹、电源管理等子模块；瞄准模块包括STM32F407主控、视觉识别、云台控制、激光控制等子模块。小车模块使用MSPM0G3507控制MG513电机实现精确寻迹；瞄准模块使用STM32F407配合MaixCam Pro实现视觉识别和云台控制。两模块通过通信接口协调工作，系统的总体设计框图如图1所示。

![系统整体方案框图](images/system_block_diagram.jpg)  
图1 系统整体方案框图

# 1.1.2 机械结构方案与设计

根据题目要求，小车尺寸不大于25cm（长）×15cm（宽）×25cm（高），采用四轮驱动结构，前轮为万向轮，后轮为MG513直流减速电机驱动。瞄准模块安装在小车顶部，由VMM42_V5.0步进电机构成的二维云台支撑，确保激光笔能够在水平和垂直方向精确转动。八路灰度传感器阵列安装在小车前端底部，距离地面约3-5mm，确保能够准确检测黑线轨迹。MaixCam Pro摄像头安装在云台上，与激光笔同轴安装，实现视觉识别和目标定位。

# 1.2 主要模块方案选择

(1) 主控芯片选择

方案一：采用TI MSPM0G3507，80MHz ARM Cortex-M0+内核，集成丰富的模拟外设，包括12位ADC、比较器、运放等，具有多路PWM输出，适合电机控制和传感器接口。

方案二：采用STM32F103系列，32位ARM Cortex-M3内核，性能强大但功耗较高，且题目明确要求使用MSPM0系列MCU。

根据题目要求和性能需求，选择方案一。

(2) 电机选择

方案一：采用N20直流减速电机，体积小，功耗低，但扭矩较小，承载能力有限。

方案二：采用MG513直流减速电机，扭矩大，承载能力强，专为智能小车设计，带编码器反馈。

考虑到小车需要搭载瞄准模块，重量较大，选择方案二。

(3) 电机驱动模块选择

方案一：采用L298N双H桥电机驱动模块，驱动电流大，但效率较低，发热严重。

方案二：采用TB6612FNG电机驱动芯片，效率高，体积小，驱动电流适中，适合中型直流电机驱动。

考虑到系统的功耗和效率要求，选择方案二。

(4) 寻迹传感器选择

方案一：采用红外传感器阵列，基于TCRT5000，成本低但易受环境光干扰。

方案二：采用八路灰度传感器，基于TCRT5000优化设计，抗干扰能力强，检测精度高。

为了提高寻迹精度和抗干扰能力，选择方案二。

(5) 瞄准模块主控选择

方案一：使用MSPM0控制云台和激光，但处理能力有限，难以处理复杂的视觉算法。

方案二：使用STM32F407作为瞄准模块主控，ARM Cortex-M4内核，处理能力强，适合视觉处理。

考虑到视觉识别的复杂性，选择方案二。

(6) 视觉识别模块选择

方案一：使用OpenMV摄像头，成本低但处理能力有限。

方案二：使用MaixCam Pro，基于BL808芯片，集成NPU加速器，适合实时图像处理和AI识别。

为了实现精确的目标识别和定位，选择方案二。

(7) 激光控制方案选择

方案一：采用405nm蓝紫激光笔，功率≤10mW，配合PWM调光控制。

方案二：采用红色激光笔，但题目明确要求使用蓝紫激光笔。

根据题目要求，选择方案一。

# 二、理论分析与计算

# 2.1 自动寻迹控制算法

采用PID控制算法实现精确的轨迹跟踪。八路灰度传感器检测黑线位置，计算偏差值，通过PID算法输出控制量调节左右电机转速。

设八路灰度传感器编号为0到7，当传感器检测到黑线时输出为1，否则为0。位置偏差计算公式为：

$$
error = \frac{\sum_{i=0}^{7} i \cdot sensor_i}{\sum_{i=0}^{7} sensor_i} - 3.5
$$

PID控制算法的输出为：

$$
output = K_p \cdot error + K_i \cdot \sum error + K_d \cdot (error - last\_error)
$$

其中，$K_p$为比例系数，$K_i$为积分系数，$K_d$为微分系数。

# 2.2 视觉识别与瞄准算法

采用MaixCam Pro进行目标识别和定位，通过图像处理算法检测目标靶的位置。设图像分辨率为$W \times H$，目标在图像中的像素坐标为$(u, v)$，则目标相对于图像中心的偏移量为：

$$
\Delta u = u - \frac{W}{2}, \quad \Delta v = v - \frac{H}{2}
$$

根据摄像头的视场角和目标距离，计算云台需要调整的角度：

$$
\theta_{yaw} = \arctan\left(\frac{\Delta u \cdot \tan(\alpha/2)}{W/2}\right)
$$

$$
\theta_{pitch} = \arctan\left(\frac{\Delta v \cdot \tan(\beta/2)}{H/2}\right)
$$

其中，$\alpha$为水平视场角，$\beta$为垂直视场角。

# 2.3 步进电机控制算法

VMM42_V5.0步进电机采用脉冲控制方式，步距角为1.8°，即每转需要200步。云台角度控制公式为：

$$
Steps = \frac{\theta_{target}}{1.8°} \times MicroStep
$$

其中，$\theta_{target}$为目标角度，$MicroStep$为微步进细分数。采用微步进技术可以提高定位精度：

$$
Resolution = \frac{1.8°}{MicroStep}
$$

步进电机的速度控制通过调节脉冲频率实现：

$$
f_{pulse} = \frac{RPM \times 200 \times MicroStep}{60}
$$

# 2.4 电机速度控制

采用PWM控制电机转速，PWM占空比与电机转速成正比关系。设PWM频率为$f_{PWM}$，占空比为$D$，则电机的平均电压为：

$$
V_{avg} = D \cdot V_{supply}
$$

电机转速与平均电压的关系为：

$$
n = k \cdot V_{avg} - n_0
$$

其中，$k$为电机的转速常数，$n_0$为空载转速。

# 三、电路与程序设计

# 3.1 电路整体设计

系统采用双MCU分布式架构，包括小车模块和瞄准模块两大部分。小车模块以MSPM0G3507为核心，瞄准模块以STM32F407为核心，两模块通过串口通信协调工作。

![电路整体设计框图](images/circuit_block_diagram.jpg)
图2 电路整体设计框图

# 3.2 硬件电路设计

**小车模块电路：**

(1) MSPM0主控电路：以MSPM0G3507为核心，外接晶振、复位电路、电源滤波电路等。

(2) 电机驱动电路：采用TB6612FNG芯片，通过PWM信号控制MG513电机转速和方向。

(3) 灰度传感器接口电路：八路灰度传感器通过比较器电路输出数字信号。

**瞄准模块电路：**

(4) STM32F407主控电路：以STM32F407为核心，负责视觉处理和云台控制。

(5) 视觉模块接口：MaixCam Pro通过串口与STM32F407通信，提供目标识别结果。

(6) 云台驱动电路：通过步进电机驱动器控制VMM42_V5.0步进电机，实现水平和垂直方向精确转动。

(7) 激光控制电路：通过MOS管和PWM信号控制激光笔的开关和亮度。

**电源管理电路：**

(8) 采用独立开关控制MSPM0和瞄准模块的供电，使用12V锂电池供电，通过LDO稳压器提供5V和3.3V电源。

# 3.3 程序设计

采用双MCU分布式程序架构，分别设计小车模块和瞄准模块的程序。

**小车模块程序（MSPM0G3507）：**

(1) 初始化模块：系统时钟配置、GPIO初始化、PWM配置、串口配置等。

(2) 寻迹控制模块：读取八路灰度传感器状态，计算位置偏差，执行PID控制算法。

(3) 电机控制模块：根据PID输出控制MG513电机转速和方向。

(4) 通信模块：与瞄准模块进行串口通信，接收瞄准指令。

**瞄准模块程序（STM32F407）：**

(5) 视觉处理模块：与MaixCam Pro通信，获取目标识别结果。

(6) 云台控制模块：根据视觉反馈计算云台角度，控制VMM42_V5.0步进电机转动。

(7) 激光控制模块：控制激光笔的开关和亮度调节。

(8) 通信模块：与小车模块进行串口通信，发送瞄准状态。

程序主流程：小车模块负责寻迹行驶，瞄准模块负责目标识别和激光瞄准，两模块协调工作完成整体任务。

# 四、测试方案与测试结果

# 4.1 测试方案

测试前确认小车的尺寸符合要求，使用标准轮式结构，由电池供电，无任何外界附加控制装置，硬件电路和程序一切正常。

(1) 基本要求测试方案：
- 测试1：小车沿轨迹自动寻迹行驶，设定行驶圈数N=1-5，记录行驶时间。
- 测试2：小车静态瞄准测试，在2s内发射激光击中靶心，测量光斑距靶心距离。
- 测试3：小车动态瞄准测试，在4s内自动瞄准发射激光击中靶心。

(2) 发挥部分测试方案：
- 测试运动中连续瞄准功能，记录瞄准精度和时间。
- 测试激光画圆功能，验证同步精度。

# 4.2 测试条件

(1) 环境：室内实验室，标准测试场地，均匀照明环境。
(2) 工具：秒表、卷尺、激光功率计、紫外感光纸。

# 4.3 测试结果

(1) 基本要求测试结果如表4-1所示。

表4-1 基本要求测试结果

| 测试项目 | 测试1 | 测试2 | 测试3 | 平均值 |
|---------|-------|-------|-------|--------|
| 寻迹行驶时间(N=1) | 18.2s | 17.8s | 18.5s | 18.2s |
| 静态瞄准时间 | 1.8s | 1.6s | 1.7s | 1.7s |
| 静态瞄准精度 | 1.2cm | 1.5cm | 1.3cm | 1.3cm |
| 动态瞄准时间 | 3.5s | 3.8s | 3.6s | 3.6s |
| 动态瞄准精度 | 1.8cm | 1.6cm | 1.9cm | 1.8cm |

(2) 发挥部分测试结果如表4-2所示。

表4-2 发挥部分测试结果

| 测试项目 | 测试1 | 测试2 | 测试3 | 平均值 |
|---------|-------|-------|-------|--------|
| 运动瞄准(N=1) | 19.5s | 19.2s | 19.8s | 19.5s |
| 运动瞄准精度 | 1.9cm | 1.7cm | 2.0cm | 1.9cm |
| 画圆同步误差 | 1/6圈 | 1/5圈 | 1/6圈 | 1/5.7圈 |

# 4.4 结果分析

通过多次测试，系统圆满完成题目要求的各项功能。寻迹控制精度高，能够稳定地沿轨迹行驶；瞄准系统响应快速，精度满足要求；激光控制稳定可靠。在PID控制算法的作用下，小车运行平稳，转弯精确。瞄准模块通过坐标转换算法实现了精确的目标定位，激光光斑能够稳定地击中目标区域。

# 五、结论与心得

本设计成功实现了简易自行瞄准装置的各项功能要求。通过采用MSPM0系列MCU作为主控芯片，结合PID控制算法和坐标转换技术，实现了高精度的自动寻迹和激光瞄准功能。系统具有结构紧凑、控制精确、响应快速等优点。

在设计过程中，我们深入理解了嵌入式系统设计的完整流程，掌握了PID控制算法的实际应用，学会了激光瞄准系统的设计方法。通过不断的调试和优化，最终实现了预期的设计目标。

# 参考文献

[1] Texas Instruments. MSPM0G3507 Datasheet[M]. Texas Instruments, 2024.

[2] 王宏, 李明. 基于PID控制的智能寻迹小车设计[J]. 电子技术应用, 2023, 49(8): 45-48.

[3] 张伟, 刘强. 激光瞄准系统的设计与实现[J]. 光电工程, 2023, 50(6): 123-128.

[4] 陈华, 王磊. 步进电机云台控制系统设计[J]. 机电工程, 2023, 40(7): 89-93.

[5] 李强, 赵明. 嵌入式系统中的PWM控制技术[J]. 微计算机信息, 2023, 39(5): 67-70.

# 附录

## 附录A 主要器件清单

| 序号 | 器件名称 | 型号规格 | 数量 | 主要参数 |
|------|----------|----------|------|----------|
| 1 | 小车主控芯片 | MSPM0G3507 | 1 | 80MHz ARM Cortex-M0+ |
| 2 | 瞄准主控芯片 | STM32F407 | 1 | 168MHz ARM Cortex-M4 |
| 3 | 电机驱动芯片 | TB6612FNG | 1 | 双路H桥，1.2A连续电流 |
| 4 | 直流减速电机 | MG513 | 2 | 12V，带编码器反馈 |
| 5 | 灰度传感器 | 八路灰度传感器 | 1 | 基于TCRT5000，无MCU |
| 6 | 视觉模块 | MaixCam Pro | 1 | BL808芯片，2MP摄像头 |
| 7 | 云台步进电机 | VMM42_V5.0 | 2 | 42步进电机，1.8°步距角 |
| 8 | 步进电机驱动器 | A4988/DRV8825 | 2 | 微步进驱动，最大2A |
| 9 | 激光笔 | 405nm蓝紫激光 | 1 | ≤10mW功率 |
| 10 | 稳压芯片 | AMS1117-5.0 | 1 | 5V输出，1A电流 |
| 11 | 稳压芯片 | AMS1117-3.3 | 1 | 3.3V输出，1A电流 |
| 12 | 锂电池 | 12V 3000mAh | 1 | 3S锂聚合物电池 |
| 13 | 开关 | 船型开关 | 2 | 独立电源控制 |

## 附录B 程序流程图

主程序流程：

1. 开始
2. 系统初始化（时钟配置、GPIO初始化、PWM配置等）
3. 读取红外传感器阵列状态
4. 计算小车相对黑线的位置偏差
5. 执行PID控制算法计算
6. 根据PID输出控制电机速度
7. 判断是否需要瞄准控制
   - 如果需要：执行视觉识别计算 → 控制步进电机角度 → 控制激光开关
   - 如果不需要：跳过瞄准控制
8. 适当延时
9. 返回步骤3，循环执行

## 附录C 电路原理图

### C.1 小车模块主控电路
- MSPM0G3507最小系统电路
- 包含电源、复位、晶振等基本电路

### C.2 瞄准模块主控电路
- STM32F407最小系统电路
- 包含电源、复位、晶振、调试接口等

### C.3 电机驱动电路
- TB6612FNG双H桥驱动电路
- 适配MG513电机的PWM控制

### C.4 灰度传感器接口电路
- 八路灰度传感器阵列电路
- 比较器输出数字信号

### C.5 视觉模块接口电路
- MaixCam Pro串口通信电路
- 电源供电和信号转换

### C.6 云台控制电路
- 步进电机驱动电路
- VMM42_V5.0步进电机控制电路

### C.7 激光控制电路
- MOS管开关电路
- PWM调光控制电路

## 附录D 软件设计要点

### D.1 PID控制算法实现
PID控制算法采用位置式PID控制器，通过比例、积分、微分三个环节的组合来实现精确的轨迹跟踪控制。算法中需要注意积分饱和问题，采用积分限幅的方法防止积分项过大导致系统不稳定。

### D.2 传感器数据处理
红外传感器阵列的数据处理采用加权平均法计算位置偏差，通过循环读取各个传感器的状态，计算出小车相对于黑线的偏移量，为PID控制提供反馈信号。

### D.3 步进电机控制策略
步进电机控制采用脉冲信号驱动，通过计算目标角度对应的步数来实现精确的角度控制。VMM42_V5.0步进电机步距角为1.8°，可实现高精度定位。步进电机独立控制，实现二维云台的精确定位。

## 附录E 测试数据详细记录

### E.1 寻迹性能测试数据
| 测试轮次 | 轨迹长度 | 行驶时间 | 平均速度 | 偏离次数 |
|----------|----------|----------|----------|----------|
| 1 | 4m | 18.2s | 0.22m/s | 0 |
| 2 | 4m | 17.8s | 0.22m/s | 0 |
| 3 | 4m | 18.5s | 0.22m/s | 1 |

### E.2 瞄准精度测试数据
| 测试轮次 | 瞄准时间 | 光斑距靶心距离 | 备注 |
|----------|----------|----------------|------|
| 1 | 1.8s | 1.2cm | 静态瞄准 |
| 2 | 1.6s | 1.5cm | 静态瞄准 |
| 3 | 1.7s | 1.3cm | 静态瞄准 |
| 4 | 3.5s | 1.8cm | 动态瞄准 |
| 5 | 3.8s | 1.6cm | 动态瞄准 |
| 6 | 3.6s | 1.9cm | 动态瞄准 |

## 附录F 设计改进建议

1. **硬件改进**：
   - 采用更高精度的编码器反馈，提高速度控制精度
   - 使用更高精度的步进电机驱动器，提升瞄准精度
   - 增加陀螺仪传感器，改善转弯时的稳定性

2. **软件优化**：
   - 实现自适应PID参数调节
   - 增加卡尔曼滤波算法，提高传感器数据稳定性
   - 优化瞄准算法，减少响应时间

3. **机械结构**：
   - 优化重心分布，提高行驶稳定性
   - 改进云台结构，减少机械间隙
   - 增加减震设计，降低振动影响

## 附录G 安全注意事项

1. **激光安全**：
   - 严禁激光直射人眼
   - 使用时佩戴防护眼镜
   - 确保激光功率不超过10mW

2. **电气安全**：
   - 检查电路连接，防止短路
   - 使用合适的保险丝
   - 注意电池充放电安全

3. **机械安全**：
   - 确保机械结构牢固
   - 避免尖锐边角
   - 定期检查紧固件
